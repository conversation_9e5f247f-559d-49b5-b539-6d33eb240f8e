<?php

namespace App\Tests\Unit;

use App\Entity\Segment;
use App\Entity\User;
use App\Entity\Mission;
use App\Service\ValidationService;
use App\Service\HeureCalculatorService;
use PHPUnit\Framework\TestCase;

class ValidationServiceTest extends TestCase
{
    private ValidationService $validationService;

    protected function setUp(): void
    {
        $heureCalculatorService = $this->createMock(HeureCalculatorService::class);
        $this->validationService = new ValidationService($heureCalculatorService);
    }

    public function testSegmentsChevauchentPourMemeUtilisateur(): void
    {
        // Créer deux utilisateurs différents avec des IDs simulés
        $user1 = new User();
        $user1->setNom('Dupont')->setPrenom('Jean');
        // Simuler un ID pour user1
        $reflection = new \ReflectionClass($user1);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($user1, 1);

        $user2 = new User();
        $user2->setNom('Martin')->setPrenom('Pierre');
        // Simuler un ID pour user2
        $reflection = new \ReflectionClass($user2);
        $idProperty = $reflection->getProperty('id');
        $idProperty->setAccessible(true);
        $idProperty->setValue($user2, 2);

        // Créer une mission
        $mission = new Mission();
        $mission->setTitre('Mission Test');

        // Créer deux segments qui se chevauchent temporellement
        $segment1 = new Segment();
        $segment1->setUser($user1)
                 ->setMission($mission)
                 ->setType(Segment::TYPE_INTERVENTION)
                 ->setDateHeureDebut(new \DateTime('2024-01-01 09:00:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-01 12:00:00'));

        $segment2 = new Segment();
        $segment2->setUser($user2) // Utilisateur différent
                 ->setMission($mission)
                 ->setType(Segment::TYPE_INTERVENTION)
                 ->setDateHeureDebut(new \DateTime('2024-01-01 10:00:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-01 13:00:00'));

        $segment3 = new Segment();
        $segment3->setUser($user1) // Même utilisateur que segment1
                 ->setMission($mission)
                 ->setType(Segment::TYPE_VOYAGE)
                 ->setDateHeureDebut(new \DateTime('2024-01-01 11:00:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-01 14:00:00'));

        // Test 1: Segments de différents utilisateurs qui se chevauchent = OK
        $validation1 = $this->validationService->validerSegment($segment1, [$segment2]);
        $this->assertTrue($validation1['valide'], 'Les segments de différents utilisateurs peuvent se chevaucher');

        // Test 2: Segments du même utilisateur qui se chevauchent = ERREUR
        $validation2 = $this->validationService->validerSegment($segment1, [$segment3]);
        $this->assertFalse($validation2['valide'], 'Les segments du même utilisateur ne peuvent pas se chevaucher');
        $this->assertNotEmpty($validation2['erreurs'], 'Il doit y avoir une erreur de chevauchement');

        // Test 3: Segments du même utilisateur qui ne se chevauchent pas = OK
        $segment4 = new Segment();
        $segment4->setUser($user1)
                 ->setMission($mission)
                 ->setType(Segment::TYPE_STAND_BY)
                 ->setDateHeureDebut(new \DateTime('2024-01-01 15:00:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-01 18:00:00'));

        $validation3 = $this->validationService->validerSegment($segment1, [$segment4]);
        $this->assertTrue($validation3['valide'], 'Les segments du même utilisateur qui ne se chevauchent pas sont valides');
    }

    public function testValidationDateFinSuperieureDateDebut(): void
    {
        $user = new User();
        $user->setNom('Test')->setPrenom('User');

        $mission = new Mission();
        $mission->setTitre('Mission Test');

        // Segment avec date fin <= date début
        $segment = new Segment();
        $segment->setUser($user)
                ->setMission($mission)
                ->setType(Segment::TYPE_INTERVENTION)
                ->setDateHeureDebut(new \DateTime('2024-01-01 12:00:00'))
                ->setDateHeureFin(new \DateTime('2024-01-01 10:00:00')); // Date fin avant date début

        $validation = $this->validationService->validerSegment($segment, []);

        $this->assertFalse($validation['valide'], 'Un segment avec date fin <= date début doit être invalide');
        $this->assertNotEmpty($validation['erreurs'], 'Il doit y avoir une erreur');
        $this->assertStringContainsString('date de fin doit être postérieure', $validation['erreurs'][0]);
    }
}
