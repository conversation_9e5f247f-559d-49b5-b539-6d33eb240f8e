import { Controller } from "@hotwired/stimulus";

/**
 * Controller Stimulus pour la sélection multiple d'utilisateurs
 */
export default class extends Controller {
    static targets = ['button', 'dropdown', 'hiddenInput', 'buttonText', 'searchInput', 'selectedUsersDisplay'];
    static values = {
        selectedUsers: Array,
        placeholder: String
    };

    connect() {
        console.log('MultiUserSelectorController connecté!', this.element);
        this.isOpen = false;
        this.allUsers = [];
        this.filteredUsers = [];
        this.selectedUserIds = new Set();
        this.setupEventListeners();
        this.ensureOptionsContainer();
    }

    disconnect() {
        document.removeEventListener('click', this.handleDocumentClick.bind(this));
    }

    setupEventListeners() {
        // Listener pour fermer le dropdown en cliquant ailleurs
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // Listener pour la recherche
        if (this.hasSearchInputTarget) {
            this.searchInputTarget.addEventListener('input', this.handleSearch.bind(this));
            this.searchInputTarget.addEventListener('keydown', this.handleSearchKeydown.bind(this));
        }
    }

    ensureOptionsContainer() {
        if (!this.hasDropdownTarget) return;

        let optionsContainer = this.dropdownTarget.querySelector('.options-container');
        if (!optionsContainer) {
            optionsContainer = document.createElement('div');
            optionsContainer.className = 'options-container max-h-60 overflow-y-auto';
            this.dropdownTarget.appendChild(optionsContainer);
        }
    }

    /**
     * Toggle l'ouverture/fermeture du dropdown
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    /**
     * Ouvre le dropdown
     */
    open() {
        this.dropdownTarget.classList.remove('hidden');
        this.isOpen = true;
        this.buttonTarget.classList.add('ring-4', 'ring-gray-100');

        if (this.hasSearchInputTarget) {
            setTimeout(() => {
                this.searchInputTarget.focus();
            }, 100);
        }
    }

    /**
     * Ferme le dropdown
     */
    close() {
        this.dropdownTarget.classList.add('hidden');
        this.isOpen = false;
        this.buttonTarget.classList.remove('ring-4', 'ring-gray-100');

        if (this.hasSearchInputTarget) {
            this.searchInputTarget.value = '';
            this.showAllUsers();
        }
    }

    /**
     * Toggle la sélection d'un utilisateur
     */
    toggleUser(event) {
        const userId = parseInt(event.currentTarget.dataset.userId);
        const userName = event.currentTarget.dataset.userName;

        if (this.selectedUserIds.has(userId)) {
            this.selectedUserIds.delete(userId);
        } else {
            this.selectedUserIds.add(userId);
        }

        this.updateSelectedUsersDisplay();
        this.updateHiddenInput();
        this.updateButtonText();
        this.updateOptionStates();
    }

    /**
     * Met à jour l'affichage des utilisateurs sélectionnés
     */
    updateSelectedUsersDisplay() {
        if (!this.hasSelectedUsersDisplayTarget) return;

        this.selectedUsersDisplayTarget.innerHTML = '';

        if (this.selectedUserIds.size === 0) {
            return;
        }

        this.selectedUserIds.forEach(userId => {
            const user = this.allUsers.find(u => u.id === userId);
            if (user) {
                const badge = document.createElement('span');
                badge.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
                badge.innerHTML = `
                    ${user.prenom} ${user.nom}
                    <button type="button"
                            class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none"
                            data-action="click->multi-user-selector#removeUser"
                            data-user-id="${userId}">
                        <svg class="w-2 h-2" fill="currentColor" viewBox="0 0 8 8">
                            <path d="m0 0l8 8m0-8l-8 8"/>
                        </svg>
                    </button>
                `;
                this.selectedUsersDisplayTarget.appendChild(badge);
            }
        });
    }

    /**
     * Supprime un utilisateur de la sélection
     */
    removeUser(event) {
        event.stopPropagation();
        const userId = parseInt(event.currentTarget.dataset.userId);
        this.selectedUserIds.delete(userId);

        this.updateSelectedUsersDisplay();
        this.updateHiddenInput();
        this.updateButtonText();
        this.updateOptionStates();

        // Déclencher l'événement de changement
        this.dispatchChangeEvent();
    }

    /**
     * Met à jour le texte du bouton
     */
    updateButtonText() {
        if (!this.hasButtonTextTarget) return;

        const count = this.selectedUserIds.size;
        if (count === 0) {
            this.buttonTextTarget.textContent = this.placeholderValue || 'Tous les utilisateurs';
        } else if (count === 1) {
            const userId = Array.from(this.selectedUserIds)[0];
            const user = this.allUsers.find(u => u.id === userId);
            this.buttonTextTarget.textContent = user ? `${user.prenom} ${user.nom}` : '1 utilisateur sélectionné';
        } else {
            this.buttonTextTarget.textContent = `${count} utilisateurs sélectionnés`;
        }
    }

    /**
     * Met à jour l'input caché
     */
    updateHiddenInput() {
        if (!this.hasHiddenInputTarget) return;

        const selectedIds = Array.from(this.selectedUserIds);
        this.hiddenInputTarget.value = JSON.stringify(selectedIds);
    }

    /**
     * Met à jour l'état visuel des options
     */
    updateOptionStates() {
        const options = this.dropdownTarget.querySelectorAll('[data-user-id]');
        options.forEach(option => {
            const userId = parseInt(option.dataset.userId);
            const checkbox = option.querySelector('input[type="checkbox"]');

            if (this.selectedUserIds.has(userId)) {
                option.classList.add('bg-blue-50');
                if (checkbox) checkbox.checked = true;
            } else {
                option.classList.remove('bg-blue-50');
                if (checkbox) checkbox.checked = false;
            }
        });
    }

    /**
     * Gère la recherche
     */
    handleSearch(event) {
        const searchTerm = event.target.value.toLowerCase();
        this.filterUsers(searchTerm);
    }

    /**
     * Filtre les utilisateurs selon le terme de recherche
     */
    filterUsers(searchTerm) {
        if (!searchTerm) {
            this.showAllUsers();
            return;
        }

        this.filteredUsers = this.allUsers.filter(user =>
            `${user.prenom} ${user.nom}`.toLowerCase().includes(searchTerm) ||
            (user.roleMetier && user.roleMetier.toLowerCase().includes(searchTerm))
        );

        this.renderUsers(this.filteredUsers);
    }

    /**
     * Affiche tous les utilisateurs
     */
    showAllUsers() {
        this.renderUsers(this.allUsers);
    }

    /**
     * Rend les utilisateurs dans le dropdown
     */
    renderUsers(users) {
        this.ensureOptionsContainer();
        const optionsContainer = this.dropdownTarget.querySelector('.options-container');
        if (!optionsContainer) return;

        optionsContainer.innerHTML = '';

        if (users.length === 0) {
            const noResult = document.createElement('div');
            noResult.className = 'px-4 py-2 text-sm text-gray-500';
            noResult.textContent = 'Aucun utilisateur trouvé';
            optionsContainer.appendChild(noResult);
            return;
        }

        users.forEach(user => {
            const option = document.createElement('label');
            option.className = 'flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer';
            option.dataset.userId = user.id;
            option.dataset.userName = `${user.prenom} ${user.nom}`;
            option.dataset.action = 'click->multi-user-selector#toggleUser';

            const isSelected = this.selectedUserIds.has(user.id);

            option.innerHTML = `
                <input type="checkbox"
                       class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       ${isSelected ? 'checked' : ''}>
                <div class="flex-1">
                    <div class="text-sm font-medium text-gray-900">${user.prenom} ${user.nom}</div>
                    <div class="text-xs text-gray-500">${user.roleMetier || 'user'}</div>
                </div>
            `;

            if (isSelected) {
                option.classList.add('bg-blue-50');
            }

            optionsContainer.appendChild(option);
        });
    }

    /**
     * Met à jour les utilisateurs disponibles
     */
    updateUsers(users) {
        console.log('updateUsers appelée avec:', users);
        this.allUsers = users;
        this.filteredUsers = [...users];
        this.showAllUsers();
    }

    /**
     * Vide la sélection
     */
    clearSelection() {
        this.selectedUserIds.clear();
        this.updateSelectedUsersDisplay();
        this.updateHiddenInput();
        this.updateButtonText();
        this.updateOptionStates();
        this.dispatchChangeEvent();
    }

    /**
     * Gère les clics en dehors du dropdown
     */
    handleDocumentClick(event) {
        if (!this.element.contains(event.target)) {
            this.close();
        }
    }

    /**
     * Gère les touches du clavier dans le champ de recherche
     */
    handleSearchKeydown(event) {
        switch (event.key) {
            case 'Escape':
                this.close();
                break;
        }
    }

    /**
     * Déclenche l'événement de changement
     */
    dispatchChangeEvent() {
        const event = new CustomEvent('userSelectionChanged', {
            detail: {
                selectedUserIds: Array.from(this.selectedUserIds),
                selectedUsers: this.allUsers.filter(u => this.selectedUserIds.has(u.id))
            },
            bubbles: true
        });
        this.element.dispatchEvent(event);
    }
}
