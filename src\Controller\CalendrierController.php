<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CalendrierController extends AbstractController
{
    #[Route('/calendrier', name: 'app_calendrier')]
    public function index(): Response
    {
        $user = $this->getUser();

        $currentUserData = [
            'id' => $user->getId(),
            'nomComplet' => $user->getNomComplet(),
            'isUser' => $user->isUser(),
            'isAdmin' => $user->isAdmin(),
            'isManager' => $user->isManager()
        ];

        // Debug log
        error_log('CalendrierController - Utilisateur connecté: ' . json_encode($currentUserData));

        return $this->render('calendrier/index.html.twig', [
            'currentUser' => $currentUserData
        ]);
    }
}
