<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CalendrierController extends AbstractController
{
    #[Route('/calendrier', name: 'app_calendrier')]
    public function index(): Response
    {
        $user = $this->getUser();
        
        return $this->render('calendrier/index.html.twig', [
            'currentUser' => [
                'id' => $user->getId(),
                'nomComplet' => $user->getNomComplet(),
                'isUser' => $user->isUser(),
                'isAdmin' => $user->isAdmin(),
                'isManager' => $user->isManager()
            ]
        ]);
    }
}
