import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static targets = [
        "calendar", "modal", "modalTitle", "form", "segmentId",
        "userDropdown", "missionDropdown", "typeDropdown",
        "dateHeureDebut", "dateHeureFin", "deleteButton"
    ];

    static values = {
        currentUser: Object
    };

    static get optionalTargets() {
        return [
            "modal", "modalTitle", "form", "segmentId",
            "userDropdown", "missionDropdown", "typeDropdown",
            "dateHeureDebut", "dateHeureFin", "deleteButton"
        ];
    }

    connect() {
        console.log('DOM chargé, initialisation du calendrier...');

        // Nettoyer toute instance précédente si elle existe
        if (window.calendarController && window.calendarController !== this) {
            console.log('Nettoyage de l\'instance précédente du calendrier');
            if (window.calendarController.calendar) {
                window.calendarController.calendar.destroy();
            }
        }

        // Stocker la référence du contrôleur globalement pour les fonctions onclick
        window.calendarController = this;

        this.calendar = null;
        this.currentSegmentId = null;
        this.allUsers = [];
        this.allMissions = [];

        // Vérifier que FullCalendar est disponible avant de continuer
        if (typeof FullCalendar === 'undefined') {
            console.log('FullCalendar pas encore chargé, attente...');
            // Attendre que FullCalendar soit chargé
            const checkFullCalendar = () => {
                if (typeof FullCalendar !== 'undefined') {
                    this.loadData();
                } else {
                    setTimeout(checkFullCalendar, 100);
                }
            };
            checkFullCalendar();
        } else {
            // Charger les données et initialiser le calendrier
            this.loadData();
        }
    }

    disconnect() {
        // Nettoyer les ressources lors de la déconnexion
        if (this.calendar) {
            this.calendar.destroy();
            this.calendar = null;
        }

        // Nettoyer les écouteurs d'événements
        this.removeGlobalDropdownListener();
        this.removeDropdownToggleListeners();

        if (this.userChangeHandler) {
            const hiddenInput = this.hasUserDropdownTarget ?
                this.userDropdownTarget.querySelector('input[name="userId"]') : null;
            if (hiddenInput) {
                hiddenInput.removeEventListener('change', this.userChangeHandler);
            }
        }

        // Nettoyer la référence globale
        if (window.calendarController === this) {
            window.calendarController = null;
        }

        console.log('Calendrier déconnecté et nettoyé');
    }

    async loadData() {
        try {
            // Toujours charger les données car elles seront nécessaires pour la modale
            const promises = [
                this.loadUsers(),
                this.loadMissions()
            ];

            await Promise.all(promises);

            // Initialiser le calendrier après le chargement des données
            this.initializeCalendar();
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            // Initialiser le calendrier même en cas d'erreur de chargement des données
            this.initializeCalendar();
        }
    }

    async loadUsers() {
        try {
            console.log('Chargement des utilisateurs...');
            const response = await ajax.get(window.calendarApiUrls.users);
            this.allUsers = response.data.data || response.data || [];
            console.log('Utilisateurs chargés:', this.allUsers);
        } catch (error) {
            console.error('Erreur lors du chargement des utilisateurs:', error);
        }
    }

    populateUserDropdown() {
        console.log('populateUserDropdown appelée');
        console.log('hasUserDropdownTarget:', this.hasUserDropdownTarget);
        console.log('allUsers:', this.allUsers);
        console.log('currentUser:', this.currentUserValue);

        if (!this.hasUserDropdownTarget) {
            console.log('Pas de userDropdownTarget');
            return;
        }

        const userController = this.application.getControllerForElementAndIdentifier(this.userDropdownTarget, 'searchable-dropdown');
        if (!userController) {
            console.log('Pas de contrôleur searchable-dropdown trouvé');
            return;
        }

        // Préparer les options pour le dropdown
        const userOptions = this.allUsers.map(user => ({
            value: user.id.toString(),
            text: `${user.prenom} ${user.nom}`,
            searchableText: `${user.prenom} ${user.nom} ${user.role}`,
            html: `
                <span class="custom-dropdown-icon">👤</span>
                ${user.prenom} ${user.nom} <span class="text-xs text-gray-500">(${user.role})</span>
            `
        }));

        userController.updateOptions(userOptions);

        // Si l'utilisateur connecté est un "user", le présélectionner et désactiver le champ
        if (this.currentUserValue && this.currentUserValue.isUser) {
            const button = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="button"]');
            const searchInput = this.userDropdownTarget.querySelector('[data-searchable-dropdown-target="searchInput"]');

            if (button) {
                button.disabled = true;
                button.classList.add('bg-gray-100', 'cursor-not-allowed');
                button.title = 'Vous ne pouvez créer des segments que pour vous-même';
            }

            if (searchInput) {
                searchInput.disabled = true;
                searchInput.classList.add('bg-gray-100', 'cursor-not-allowed');
            }

            // Présélectionner l'utilisateur connecté
            userController.selectOption(this.currentUserValue.id.toString(), true);
        }

        console.log('Dropdown peuplé avec', this.allUsers.length, 'utilisateurs');
    }

    async loadMissions() {
        try {
            console.log('Chargement des missions...');
            const response = await ajax.get(window.calendarApiUrls.missions);
            console.log('Réponse brute API missions:', response);

            // Essayer différents formats de réponse
            if (response.data && Array.isArray(response.data)) {
                this.allMissions = response.data;
            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                this.allMissions = response.data.data;
            } else if (Array.isArray(response)) {
                this.allMissions = response;
            } else {
                console.error('Format de réponse missions inattendu:', response);
                this.allMissions = [];
            }

            console.log('Missions chargées:', this.allMissions);
            console.log('Nombre de missions:', this.allMissions.length);
        } catch (error) {
            console.error('Erreur lors du chargement des missions:', error);
        }
    }

    populateAllMissions() {
        console.log('populateAllMissions appelée');
        console.log('hasMissionDropdownTarget:', this.hasMissionDropdownTarget);
        console.log('allMissions:', this.allMissions);

        if (!this.hasMissionDropdownTarget) {
            console.log('Pas de missionDropdownTarget');
            return;
        }

        const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
        if (!missionController) {
            console.log('Pas de contrôleur searchable-dropdown trouvé');
            return;
        }

        // Préparer les options pour le dropdown
        const missionOptions = this.allMissions.map(mission => {
            // Afficher les utilisateurs assignés
            let assignedUsers = '';
            if (mission.users && mission.users.length > 0) {
                assignedUsers = mission.users.map(u => `${u.prenom} ${u.nom}`).join(', ');
            } else {
                assignedUsers = 'Non assigné';
            }

            return {
                value: mission.id.toString(),
                text: mission.titre,
                searchableText: `${mission.titre} ${mission.pays} ${assignedUsers}`,
                html: `
                    <span class="custom-dropdown-icon">📋</span>
                    <div>
                        <div class="font-medium">${mission.titre}</div>
                        <div class="text-xs text-gray-500">${mission.pays} - ${assignedUsers}</div>
                    </div>
                `
            };
        });

        missionController.updateOptions(missionOptions);

        console.log('Mission dropdown peuplé avec', this.allMissions.length, 'missions');
    }

    filterMissionsByUser(userId) {
        if (!this.hasMissionDropdownTarget) return;

        console.log('Utilisateur sélectionné:', userId);

        const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
        if (!missionController) return;

        if (!userId) {
            // Si aucun utilisateur sélectionné, afficher toutes les missions
            this.populateAllMissions();
            return;
        }

        const userMissions = this.allMissions.filter(mission => {
            // Vérifier si l'utilisateur est assigné à cette mission
            return mission.users && mission.users.some(user => user.id == userId);
        });

        console.log(`Missions filtrées pour l'utilisateur ${userId}:`, userMissions);

        if (userMissions.length === 0) {
            missionController.updateOptions([{
                value: '',
                text: 'Aucune mission assignée à cet utilisateur',
                html: `
                    <span class="custom-dropdown-icon">❌</span>
                    <span class="text-gray-500">Aucune mission assignée à cet utilisateur</span>
                `
            }]);
            return;
        }

        // Préparer les options filtrées
        const filteredOptions = userMissions.map(mission => ({
            value: mission.id.toString(),
            text: mission.titre,
            searchableText: `${mission.titre} ${mission.pays}`,
            html: `
                <span class="custom-dropdown-icon">📋</span>
                <div>
                    <div class="font-medium">${mission.titre}</div>
                    <div class="text-xs text-gray-500">${mission.pays}</div>
                </div>
            `
        }));

        missionController.updateOptions(filteredOptions);
    }

    initializeCalendar() {
        console.log('Élément calendrier trouvé:', this.calendarTarget);
        console.log('FullCalendar disponible:', typeof FullCalendar);

        if (!this.calendarTarget) {
            console.error('Élément calendrier non trouvé !');
            return;
        }

        if (typeof FullCalendar === 'undefined') {
            console.error('FullCalendar non chargé !');
            return;
        }

        this.calendar = new FullCalendar.Calendar(this.calendarTarget, {
            initialView: 'timeGridWeek',
            locale: 'fr',
            timeZone: 'local',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            height: 'auto',
            slotMinTime: '06:00:00',
            slotMaxTime: '22:00:00',
            businessHours: [
                {
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '09:00',
                    endTime: '12:00'
                },
                {
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '14:00',
                    endTime: '18:00'
                }
            ],
            selectable: true,
            selectMirror: true,
            editable: true,
            eventResizableFromStart: true,
            events: (info, successCallback, failureCallback) => {
                const url = window.calendarApiUrls.segmentCalendar + '?start=' + encodeURIComponent(info.startStr) + '&end=' + encodeURIComponent(info.endStr);
                console.log('Chargement des événements depuis:', url);
                ajax.get(url)
                .then(response => {
                    console.log('Événements reçus:', response);
                    successCallback(response.data);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des événements:', error);
                    failureCallback(error);
                });
            },
            select: async (info) => {
                await this.openSegmentModal();
                if (this.hasDateHeureDebutTarget) {
                    this.dateHeureDebutTarget.value = info.startStr.slice(0, 16);
                }
                if (this.hasDateHeureFinTarget) {
                    this.dateHeureFinTarget.value = info.endStr.slice(0, 16);
                }
            },
            eventClick: async (info) => {
                await this.editSegment(info.event);
            },
            eventDrop: (info) => {
                this.updateSegmentDates(info.event);
            },
            eventResize: (info) => {
                this.updateSegmentDates(info.event);
            }
        });

        this.calendar.render();
        console.log('Calendrier rendu');
    }

    async openSegmentModal() {
        console.log('openSegmentModal appelée');
        this.currentSegmentId = null;

        // Charger les données si elles ne sont pas encore chargées
        await this.ensureDataLoaded();

        if (this.hasModalTitleTarget) {
            this.modalTitleTarget.textContent = 'Ajouter un segment';
        }

        if (this.hasDeleteButtonTarget) {
            this.deleteButtonTarget.classList.add('hidden');
        }

        this.openModal();
    }

    async ensureDataLoaded() {
        // Charger les données seulement si nécessaire
        const promises = [];

        if (this.allUsers.length === 0) {
            promises.push(this.loadUsers());
        }

        if (this.allMissions.length === 0) {
            promises.push(this.loadMissions());
        }

        if (promises.length > 0) {
            try {
                await Promise.all(promises);
            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
            }
        }

        // Attendre un peu que la modale soit complètement rendue
        setTimeout(() => {
            // Peupler les dropdowns
            if (this.hasUserDropdownTarget && this.allUsers.length > 0) {
                this.populateUserDropdown();
            }

            if (this.hasMissionDropdownTarget && this.allMissions.length > 0) {
                this.populateAllMissions();
            }

            // Ajouter l'écouteur d'événements pour le changement d'utilisateur
            this.setupUserChangeListener();
        }, 100);
    }

    setupUserChangeListener() {
        if (this.hasUserDropdownTarget) {
            const hiddenInput = this.userDropdownTarget.querySelector('input[name="userId"]');
            if (hiddenInput) {
                // Supprimer l'ancien écouteur s'il existe
                hiddenInput.removeEventListener('change', this.userChangeHandler);

                // Créer et ajouter le nouvel écouteur
                this.userChangeHandler = (event) => {
                    console.log('Changement d\'utilisateur détecté:', event.target.value);
                    this.filterMissionsByUser(event.target.value);

                    // Réinitialiser la sélection de mission quand l'utilisateur change
                    if (this.hasMissionDropdownTarget) {
                        const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
                        if (missionController) {
                            missionController.reset();
                        }
                    }
                };

                hiddenInput.addEventListener('change', this.userChangeHandler);
            }
        }

        // Ajouter des écouteurs pour fermer les dropdowns quand on clique sur d'autres éléments
        this.setupDropdownCloseListeners();
    }

    setupDropdownCloseListeners() {
        // Ajouter des écouteurs sur tous les inputs de la modale
        if (this.hasFormTarget) {
            const inputs = this.formTarget.querySelectorAll('input[type="datetime-local"]');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    this.closeAllDropdowns();
                });
            });
        }

        // Ajouter des écouteurs sur les boutons des dropdowns pour fermer les autres
        this.setupDropdownToggleListeners();
    }

    setupDropdownToggleListeners() {
        const searchableDropdowns = [
            { target: this.hasUserDropdownTarget ? this.userDropdownTarget : null, name: 'user', type: 'searchable-dropdown' },
            { target: this.hasMissionDropdownTarget ? this.missionDropdownTarget : null, name: 'mission', type: 'searchable-dropdown' }
        ].filter(item => item.target !== null);

        const customDropdowns = [
            { target: this.hasTypeDropdownTarget ? this.typeDropdownTarget : null, name: 'type', type: 'custom-dropdown' }
        ].filter(item => item.target !== null);

        const allDropdowns = [...searchableDropdowns, ...customDropdowns];

        allDropdowns.forEach((dropdown, index) => {
            const buttonSelector = dropdown.type === 'searchable-dropdown' ?
                '[data-searchable-dropdown-target="button"]' :
                '[data-custom-dropdown-target="button"]';

            const button = dropdown.target.querySelector(buttonSelector);
            if (button) {
                // Créer un handler unique pour chaque dropdown
                const handler = (event) => {
                    // Petite temporisation pour laisser le dropdown s'ouvrir d'abord
                    setTimeout(() => {
                        // Fermer tous les autres dropdowns
                        allDropdowns.forEach((otherDropdown, otherIndex) => {
                            if (otherIndex !== index) {
                                const controller = this.application.getControllerForElementAndIdentifier(otherDropdown.target, otherDropdown.type);
                                if (controller && controller.isOpen) {
                                    controller.close();
                                }
                            }
                        });
                    }, 10);
                };

                // Stocker le handler pour pouvoir le supprimer plus tard
                button._calendarDropdownHandler = handler;
                button.addEventListener('click', handler);
            }
        });
    }

    closeAllDropdowns() {
        // Fermer tous les dropdowns ouverts dans la modale
        const searchableDropdowns = [
            this.hasUserDropdownTarget ? this.userDropdownTarget : null,
            this.hasMissionDropdownTarget ? this.missionDropdownTarget : null
        ].filter(Boolean);

        const customDropdowns = [
            this.hasTypeDropdownTarget ? this.typeDropdownTarget : null
        ].filter(Boolean);

        // Fermer les dropdowns searchable
        searchableDropdowns.forEach(dropdown => {
            const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'searchable-dropdown');
            if (controller && controller.isOpen) {
                controller.close();
            }
        });

        // Fermer les dropdowns custom
        customDropdowns.forEach(dropdown => {
            const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'custom-dropdown');
            if (controller && controller.isOpen) {
                controller.close();
            }
        });
    }

    addGlobalDropdownListener() {
        // Créer l'écouteur s'il n'existe pas déjà
        if (!this.globalDropdownListener) {
            this.globalDropdownListener = (event) => {
                // Vérifier si le clic est en dehors des dropdowns
                const dropdowns = [
                    this.hasUserDropdownTarget ? this.userDropdownTarget : null,
                    this.hasMissionDropdownTarget ? this.missionDropdownTarget : null,
                    this.hasTypeDropdownTarget ? this.typeDropdownTarget : null
                ].filter(Boolean);

                const clickedInsideDropdown = dropdowns.some(dropdown =>
                    dropdown && dropdown.contains(event.target)
                );

                if (!clickedInsideDropdown) {
                    this.closeAllDropdowns();
                }
            };
        }

        // Ajouter l'écouteur au document
        document.addEventListener('click', this.globalDropdownListener);
    }

    removeGlobalDropdownListener() {
        if (this.globalDropdownListener) {
            document.removeEventListener('click', this.globalDropdownListener);
        }
    }

    removeDropdownToggleListeners() {
        const searchableDropdowns = [
            this.hasUserDropdownTarget ? this.userDropdownTarget : null,
            this.hasMissionDropdownTarget ? this.missionDropdownTarget : null
        ].filter(Boolean);

        const customDropdowns = [
            this.hasTypeDropdownTarget ? this.typeDropdownTarget : null
        ].filter(Boolean);

        // Supprimer les écouteurs des dropdowns searchable
        searchableDropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('[data-searchable-dropdown-target="button"]');
            if (button && button._calendarDropdownHandler) {
                button.removeEventListener('click', button._calendarDropdownHandler);
                delete button._calendarDropdownHandler;
            }
        });

        // Supprimer les écouteurs des dropdowns custom
        customDropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('[data-custom-dropdown-target="button"]');
            if (button && button._calendarDropdownHandler) {
                button.removeEventListener('click', button._calendarDropdownHandler);
                delete button._calendarDropdownHandler;
            }
        });
    }

    openModal() {
        console.log('openModal appelée');
        if (this.hasModalTarget) {
            this.modalTarget.classList.remove('hidden');

            // Ajouter l'animation d'entrée
            const modalContent = this.modalTarget.querySelector('.modal-content');
            if (modalContent) {
                modalContent.classList.add('modal-enter');
                // Retirer la classe d'animation après l'animation
                setTimeout(() => {
                    modalContent.classList.remove('modal-enter');
                }, 300);
            }

            // Ajouter un écouteur global pour fermer les dropdowns
            this.addGlobalDropdownListener();

            console.log('Modal ouvert');
        } else {
            console.error('Modal non trouvé');
        }
    }

    closeModal() {
        if (this.hasModalTarget) {
            this.modalTarget.classList.add('hidden');
        }

        // Fermer tous les dropdowns avant de fermer la modale
        this.closeAllDropdowns();

        // Supprimer les écouteurs
        this.removeGlobalDropdownListener();
        this.removeDropdownToggleListeners();

        this.resetForm();
    }

    closeModalOnBackdrop(event) {
        // Fermer seulement si on clique sur l'arrière-plan (pas sur le contenu)
        if (event.target === event.currentTarget) {
            this.closeModal();
        }
    }

    resetForm() {
        if (this.hasFormTarget) {
            this.formTarget.reset();
        }
        this.currentSegmentId = null;

        // Réinitialiser tous les dropdowns searchable
        const dropdowns = [
            this.hasUserDropdownTarget ? this.userDropdownTarget : null,
            this.hasMissionDropdownTarget ? this.missionDropdownTarget : null
        ].filter(Boolean);

        dropdowns.forEach(dropdown => {
            const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'searchable-dropdown');
            if (controller) {
                controller.reset();
            }
        });

        // Réinitialiser le dropdown de type (custom-dropdown)
        if (this.hasTypeDropdownTarget) {
            const typeController = this.application.getControllerForElementAndIdentifier(this.typeDropdownTarget, 'custom-dropdown');
            if (typeController) {
                typeController.selectedValue = '';
                if (typeController.hasButtonTextTarget) {
                    typeController.buttonTextTarget.textContent = 'Sélectionner un type';
                }
                if (typeController.hasHiddenInputTarget) {
                    typeController.hiddenInputTarget.value = '';
                }
                typeController.updateOptionStates('');
            }
        }

        // Si l'utilisateur connecté est un "user", le représélectionner
        if (this.currentUserValue && this.currentUserValue.isUser && this.hasUserDropdownTarget) {
            setTimeout(() => {
                const userController = this.application.getControllerForElementAndIdentifier(this.userDropdownTarget, 'searchable-dropdown');
                if (userController) {
                    userController.selectOption(this.currentUserValue.id.toString(), true);
                    // Filtrer les missions pour cet utilisateur
                    this.filterMissionsByUser(this.currentUserValue.id.toString());
                }
            }, 100);
        }
    }

    async editSegment(event) {
        this.currentSegmentId = event.id;

        // Charger les données si elles ne sont pas encore chargées
        await this.ensureDataLoaded();

        if (this.hasModalTitleTarget) {
            this.modalTitleTarget.textContent = 'Modifier le segment';
        }

        if (this.hasDeleteButtonTarget) {
            this.deleteButtonTarget.classList.remove('hidden');
        }

        // Remplir le formulaire avec les données de l'événement
        if (this.hasSegmentIdTarget) {
            this.segmentIdTarget.value = event.id;
        }

        // Sélectionner le type
        if (event.extendedProps.type && this.hasTypeDropdownTarget) {
            const typeController = this.application.getControllerForElementAndIdentifier(this.typeDropdownTarget, 'custom-dropdown');
            if (typeController) {
                typeController.selectOption(event.extendedProps.type, true);
            }
        }

        // Sélectionner l'utilisateur et filtrer les missions
        if (event.extendedProps.userId && this.hasUserDropdownTarget) {
            const userController = this.application.getControllerForElementAndIdentifier(this.userDropdownTarget, 'searchable-dropdown');
            if (userController) {
                userController.selectOption(event.extendedProps.userId.toString(), true);
                // Filtrer les missions pour cet utilisateur
                this.filterMissionsByUser(event.extendedProps.userId.toString());
            }
        }

        // Sélectionner la mission
        if (event.extendedProps.missionId && this.hasMissionDropdownTarget) {
            // Attendre un peu que les missions soient filtrées
            setTimeout(() => {
                const missionController = this.application.getControllerForElementAndIdentifier(this.missionDropdownTarget, 'searchable-dropdown');
                if (missionController) {
                    missionController.selectOption(event.extendedProps.missionId.toString(), true);
                }
            }, 100);
        }

        // Convertir les dates ISO en format datetime-local
        const dateDebut = new Date(event.start);
        const dateFin = new Date(event.end);

        // Formater pour datetime-local (YYYY-MM-DDTHH:mm)
        if (this.hasDateHeureDebutTarget) {
            this.dateHeureDebutTarget.value = this.formatDateTimeLocal(dateDebut);
        }

        if (this.hasDateHeureFinTarget) {
            this.dateHeureFinTarget.value = this.formatDateTimeLocal(dateFin);
        }

        this.openModal();
    }

    formatDateTimeLocal(date) {
        // La date vient de FullCalendar qui l'affiche déjà en heure locale
        // On utilise directement cette date sans conversion supplémentaire
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    async updateSegmentDates(event) {
        try {
            // Convertir les dates FullCalendar en UTC
            const dateDebut = new Date(event.start);
            const dateFin = new Date(event.end);

            await ajax.put(window.calendarApiUrls.segmentUpdate + event.id, {
                dateHeureDebut: dateDebut.toISOString(),
                dateHeureFin: dateFin.toISOString()
            });
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);

            let errorMessage = 'Erreur lors de la mise à jour du segment';

            if (error.data && error.data.errors) {
                if (Array.isArray(error.data.errors)) {
                    errorMessage = error.data.errors.join('\n');
                } else {
                    errorMessage = error.data.errors;
                }
            }

            alert(errorMessage);
            event.revert();
        }
    }

    async deleteSegment() {
        if (!this.currentSegmentId) return;

        if (confirm('Êtes-vous sûr de vouloir supprimer ce segment ?')) {
            try {
                await ajax.delete(window.calendarApiUrls.segmentDelete + this.currentSegmentId);
                this.calendar.refetchEvents();
                this.closeModal();
            } catch (error) {
                console.error('Erreur lors de la suppression:', error);
                alert('Erreur lors de la suppression du segment');
            }
        }
    }

    async submitForm(event) {
        event.preventDefault();

        if (!this.hasFormTarget) {
            console.error('Formulaire non trouvé');
            return;
        }

        const formData = new FormData(event.target);

        // Convertir les dates locales en UTC pour le serveur
        const dateDebut = new Date(formData.get('dateHeureDebut'));
        const dateFin = new Date(formData.get('dateHeureFin'));

        const data = {
            userId: parseInt(formData.get('userId')),
            missionId: parseInt(formData.get('missionId')),
            type: formData.get('type'),
            dateHeureDebut: dateDebut.toISOString(),
            dateHeureFin: dateFin.toISOString()
        };

        try {
            if (this.currentSegmentId) {
                await ajax.put(window.calendarApiUrls.segmentUpdate + this.currentSegmentId, data);
            } else {
                await ajax.post(window.calendarApiUrls.segmentCreate, data);
            }

            if (this.calendar) {
                this.calendar.refetchEvents();
            }
            this.closeModal();
        } catch (error) {
            console.error('Erreur lors de l\'enregistrement:', error);

            let errorMessage = 'Erreur lors de l\'enregistrement du segment';

            if (error.data && error.data.errors) {
                if (Array.isArray(error.data.errors)) {
                    errorMessage = error.data.errors.join('\n');
                } else {
                    errorMessage = error.data.errors;
                }
            }

            alert(errorMessage);
        }
    }
}
